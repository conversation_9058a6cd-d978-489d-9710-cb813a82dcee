import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

/**
 * Helper function to get user identification from context
 * Returns either authenticated user ID or anonymous session ID
 */
export function getUserIdentification(ctx: any) {
  const user = ctx.auth.getUserIdentity();
  
  if (user) {
    return {
      type: "authenticated" as const,
      userId: user.subject,
      anonymousId: null,
    };
  }
  
  // For anonymous users, we'll get the session ID from the request headers
  // This will be set by the API route based on the X-Rate-Limit-Identifier header
  const anonymousId = ctx.anonymousId;
  
  if (anonymousId) {
    return {
      type: "anonymous" as const,
      userId: null,
      anonymousId,
    };
  }
  
  return null;
}

/**
 * Helper function to check if a user (authenticated or anonymous) owns a resource
 */
export function isResourceOwner(
  userIdentification: ReturnType<typeof getUserIdentification>,
  resource: { userId?: string; anonymousId?: string }
) {
  if (!userIdentification) return false;
  
  if (userIdentification.type === "authenticated") {
    return resource.userId === userIdentification.userId;
  } else {
    return resource.anonymousId === userIdentification.anonymousId;
  }
}

/**
 * Create an anonymous user session
 * This is called when an anonymous user first interacts with the system
 */
export const createAnonymousSession = mutation({
  args: { 
    sessionId: v.string(),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Store anonymous session info if needed for analytics/tracking
    // For now, we'll just return the session ID
    return {
      sessionId: args.sessionId,
      createdAt: Date.now(),
    };
  },
});

/**
 * Get anonymous user info by session ID
 */
export const getAnonymousUser = query({
  args: { sessionId: v.string() },
  handler: async (ctx, args) => {
    return {
      sessionId: args.sessionId,
      type: "anonymous" as const,
      displayName: "Anonymous User",
    };
  },
});
