import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getUserIdentification, isResourceOwner } from "./anonymous";

export const createThread = mutation({
  args: {
    title: v.optional(v.string()),
    anonymousId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      throw new Error("Not authenticated and no anonymous ID provided");
    }

    const threadData = {
      updatedAt: Date.now() + 1,
      title: args.title ?? "New Chat",
      userId: user?.subject,
      anonymousId: args.anonymousId,
    };

    return await ctx.db.insert("threads", threadData);
  },
});

export const branchThread = mutation({
  args: {
    threadId: v.id("threads"),
    lastMessageCreatedAt: v.number(),
    anonymousId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      throw new Error("Not authenticated and no anonymous ID provided");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) throw new Error("Thread not found");

    // Check ownership for both authenticated and anonymous users
    const isOwner = user ? thread.userId === user.subject : thread.anonymousId === args.anonymousId;

    if (!isOwner) throw new Error("Not authorized");

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_threadId", (q) =>
        q.eq("threadId", args.threadId).lte("_creationTime", args.lastMessageCreatedAt),
      )
      .order("asc")
      .collect();

    const newThreadId = await ctx.db.insert("threads", {
      updatedAt: Date.now() + 1,
      userId: user?.subject,
      anonymousId: args.anonymousId,
      title: thread.title,
      branchedFrom: args.threadId,
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const { _id, _creationTime, ...message } of messages) {
      await ctx.db.insert("messages", {
        ...message,
        createdAt: Date.now(),
        updatedAt: Date.now() + 1,
        userId: user?.subject,
        anonymousId: args.anonymousId,
        threadId: newThreadId,
        messageId: crypto.randomUUID(),
      });
    }

    return newThreadId;
  },
});

export const getAllThreads = query({
  args: { anonymousId: v.optional(v.string()) },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      return [];
    }

    let data;
    if (user) {
      // Get threads for authenticated user
      data = await ctx.db
        .query("threads")
        .withIndex("by_userId", (q) => q.eq("userId", user.subject))
        .collect();
    } else {
      // Get threads for anonymous user
      data = await ctx.db
        .query("threads")
        .withIndex("by_anonymousId", (q) => q.eq("anonymousId", args.anonymousId))
        .collect();
    }

    return data
      .sort((a, b) => b.updatedAt - a.updatedAt)
      .map((thread) => ({
        ...thread,
        pinned: thread.pinned ?? false,
      }));
  },
});

export const updateThreadTitle = mutation({
  args: {
    threadId: v.id("threads"),
    title: v.string(),
    anonymousId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      throw new Error("Not authenticated and no anonymous ID provided");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) throw new Error("Thread not found");

    // Check ownership for both authenticated and anonymous users
    const isOwner = user ? thread.userId === user.subject : thread.anonymousId === args.anonymousId;

    if (!isOwner) throw new Error("Not authorized");

    await ctx.db.patch(args.threadId, { title: args.title });
  },
});

export const deleteThread = mutation({
  args: {
    threadId: v.id("threads"),
    anonymousId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      throw new Error("Not authenticated and no anonymous ID provided");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) throw new Error("Thread not found");

    // Check ownership for both authenticated and anonymous users
    const isOwner = user ? thread.userId === user.subject : thread.anonymousId === args.anonymousId;

    if (!isOwner) throw new Error("Not authorized");

    await ctx.db.delete(args.threadId);

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_threadId", (q) => q.eq("threadId", args.threadId))
      .collect();

    for (const message of messages) {
      await ctx.db.delete(message._id);
    }
  },
});

export const pinThread = mutation({
  args: {
    threadId: v.id("threads"),
    pinned: v.boolean(),
    anonymousId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();

    // Support both authenticated and anonymous users
    if (!user && !args.anonymousId) {
      throw new Error("Not authenticated and no anonymous ID provided");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) throw new Error("Thread not found");

    // Check ownership for both authenticated and anonymous users
    const isOwner = user ? thread.userId === user.subject : thread.anonymousId === args.anonymousId;

    if (!isOwner) throw new Error("Not authorized");

    await ctx.db.patch(args.threadId, { pinned: args.pinned });
  },
});
