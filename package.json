{"name": "ai-chat", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsgo --noEmit", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsgo --noEmit"}, "dependencies": {"@ai-sdk/deepseek": "^1.0.0-beta.1", "@ai-sdk/google": "^2.0.0-beta.1", "@ai-sdk/openai": "^2.0.0-beta.1", "@ai-sdk/react": "^2.0.0-beta.1", "@base-ui-components/react": "^1.0.0-beta.0", "@clerk/nextjs": "^6.23.1", "@clerk/react-router": "^1.6.2", "@clerk/themes": "^2.2.52", "@convex-dev/r2": "^0.6.2", "@convex-dev/react-query": "^0.0.0-alpha.11", "@nivo/calendar": "^0.99.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.33.0", "@serwist/next": "^9.0.15", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-pacer": "^0.8.0", "@tanstack/react-query": "^5.81.5", "@tanstack/react-virtual": "^3.13.12", "@uidotdev/usehooks": "^2.4.1", "@vercel/analytics": "^1.5.0", "@vercel/functions": "^2.2.2", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.0-beta.1", "botid": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.25.0", "convex-helpers": "^0.1.95", "cookies-next": "^6.0.0", "ioredis": "^5.6.1", "katex": "^0.16.22", "lodash.throttle": "^4.1.1", "lucide-react": "^0.525.0", "marked": "^16.0.0", "next": "^15.3.4", "next-themes": "^0.4.6", "posthog-js": "^1.255.1", "posthog-node": "^5.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router": "^7.6.3", "react-shiki": "^0.7.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resumable-stream": "^2.2.1", "server-only": "^0.0.1", "serwist": "^9.0.15", "shiki": "^3.7.0", "sonner": "^2.0.5", "svix": "^1.68.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@convex-dev/eslint-plugin": "^0.0.1-alpha.4", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/lodash.throttle": "^4.1.9", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript/native-preview": "^7.0.0-dev.20250628.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9.30.0", "eslint-config-next": "^15.3.4", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "trustedDependencies": ["@clerk/shared", "@tailwindcss/oxide", "@vercel/speed-insights", "core-js", "esbuild", "sharp"]}