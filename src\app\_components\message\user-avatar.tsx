import { useUser } from "@clerk/react-router";
import { UserIcon } from "lucide-react";

import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { useAnonymousUser } from "../provider/anonymous-user-provider";

export function UserAvatar() {
  const { user: clerkUser } = useUser();
  const { user: anonUser } = useAnonymousUser();

  // Show authenticated user avatar if available
  if (clerkUser) {
    return (
      <Avatar className="size-11 shrink-0 rounded-md border">
        <AvatarImage src={clerkUser.imageUrl} alt={clerkUser.username!} />
        <AvatarFallback className="bg-muted size-full rounded-md">You</AvatarFallback>
      </Avatar>
    );
  }

  // Show anonymous user avatar
  if (anonUser?.isAnonymous) {
    return (
      <Avatar className="size-11 shrink-0 rounded-md border">
        <AvatarFallback className="bg-muted size-full rounded-md">
          <UserIcon className="size-5" />
        </AvatarFallback>
      </Avatar>
    );
  }

  // Fallback
  return (
    <Avatar className="size-11 shrink-0 rounded-md border">
      <AvatarFallback className="bg-muted size-full rounded-md">You</AvatarFallback>
    </Avatar>
  );
}
