"use client";

import { useUser } from "@clerk/react-router";
import { createContext, useContext, useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

type AnonymousUser = {
  sessionId: string;
  displayName: string;
  isAnonymous: true;
};

type AuthenticatedUser = {
  isAnonymous: false;
};

type UserContextType = {
  user: AnonymousUser | AuthenticatedUser | null;
  isLoaded: boolean;
  getSessionId: () => string | null;
};

const AnonymousUserContext = createContext<UserContextType | undefined>(undefined);

export function AnonymousUserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AnonymousUser | AuthenticatedUser | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const { isLoaded: clerkLoaded, isSignedIn } = useUser();

  useEffect(() => {
    if (!clerkLoaded) return;

    if (isSignedIn) {
      // User is authenticated via Clerk
      setUser({ isAnonymous: false });
    } else {
      // User is not authenticated, create/get anonymous session
      const sessionId = getOrCreateAnonymousSession();
      setUser({
        sessionId,
        displayName: "Anonymous User",
        isAnonymous: true,
      });
    }

    setIsLoaded(true);
  }, [clerkLoaded, isSignedIn]);

  const getOrCreateAnonymousSession = (): string => {
    const existingSessionId = localStorage.getItem("anonymous_session_id");

    if (existingSessionId) {
      return existingSessionId;
    }

    const newSessionId = `anon_${uuidv4()}`;
    localStorage.setItem("anonymous_session_id", newSessionId);
    return newSessionId;
  };

  const getSessionId = (): string | null => {
    if (user && user.isAnonymous) {
      return user.sessionId;
    }
    return null;
  };

  return (
    <AnonymousUserContext.Provider value={{ user, isLoaded, getSessionId }}>
      {children}
    </AnonymousUserContext.Provider>
  );
}

export function useAnonymousUser() {
  const context = useContext(AnonymousUserContext);
  if (context === undefined) {
    throw new Error("useAnonymousUser must be used within an AnonymousUserProvider");
  }
  return context;
}

// Hook to get user identification for Convex calls
export function useUserIdentification() {
  const { user } = useAnonymousUser();

  if (!user) {
    return null;
  }

  if (user.isAnonymous) {
    return {
      type: "anonymous" as const,
      anonymousId: user.sessionId,
    };
  }

  return {
    type: "authenticated" as const,
    anonymousId: undefined,
  };
}
