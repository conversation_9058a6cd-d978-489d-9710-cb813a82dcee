"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

type AnonymousUser = {
  sessionId: string;
  displayName: string;
  isAnonymous: true;
};

type AuthenticatedUser = {
  isAnonymous: false;
};

type UserContextType = {
  user: AnonymousUser | AuthenticatedUser | null;
  isLoaded: boolean;
  getSessionId: () => string | null;
};

const AnonymousUserContext = createContext<UserContextType | undefined>(undefined);

export function AnonymousUserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AnonymousUser | AuthenticatedUser | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check if user is authenticated via Clerk
    const checkAuthStatus = async () => {
      try {
        // Try to get the current user from Clerk
        const response = await fetch("/api/auth/user", { credentials: "include" });
        
        if (response.ok) {
          // User is authenticated
          setUser({ isAnonymous: false });
        } else {
          // User is not authenticated, create/get anonymous session
          const sessionId = getOrCreateAnonymousSession();
          setUser({
            sessionId,
            displayName: "Anonymous User",
            isAnonymous: true,
          });
        }
      } catch (error) {
        // Fallback to anonymous user
        const sessionId = getOrCreateAnonymousSession();
        setUser({
          sessionId,
          displayName: "Anonymous User",
          isAnonymous: true,
        });
      } finally {
        setIsLoaded(true);
      }
    };

    checkAuthStatus();
  }, []);

  const getOrCreateAnonymousSession = (): string => {
    const existingSessionId = localStorage.getItem("anonymous_session_id");
    
    if (existingSessionId) {
      return existingSessionId;
    }

    const newSessionId = `anon_${uuidv4()}`;
    localStorage.setItem("anonymous_session_id", newSessionId);
    return newSessionId;
  };

  const getSessionId = (): string | null => {
    if (user && user.isAnonymous) {
      return user.sessionId;
    }
    return null;
  };

  return (
    <AnonymousUserContext.Provider value={{ user, isLoaded, getSessionId }}>
      {children}
    </AnonymousUserContext.Provider>
  );
}

export function useAnonymousUser() {
  const context = useContext(AnonymousUserContext);
  if (context === undefined) {
    throw new Error("useAnonymousUser must be used within an AnonymousUserProvider");
  }
  return context;
}

// Hook to get user identification for Convex calls
export function useUserIdentification() {
  const { user } = useAnonymousUser();
  
  if (!user) {
    return null;
  }

  if (user.isAnonymous) {
    return {
      type: "anonymous" as const,
      anonymousId: user.sessionId,
    };
  }

  return {
    type: "authenticated" as const,
    anonymousId: undefined,
  };
}
