import { useUser } from "@clerk/react-router";
import { NavLink } from "react-router";
import { UserIcon } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { useAnonymousUser } from "../provider/anonymous-user-provider";

export function ThreadUserProfile() {
  const { isLoaded, isSignedIn, user } = useUser();
  const { user: anonUser } = useAnonymousUser();

  // Show authenticated user profile
  if (isLoaded && isSignedIn && user) {
    const fallback = user.username
      ?.split(" ")
      .map((name) => name[0])
      .join("");

    return (
      <NavLink
        to="/auth/settings"
        className="hover:bg-primary/20 hover:border-primary/30 flex gap-2 rounded-md border border-transparent p-2 transition-colors"
      >
        <Avatar className="size-11 rounded-md">
          <AvatarImage src={user.imageUrl} alt={user.username!} />
          <AvatarFallback className="bg-primary text-primary-foreground text-sm">
            {fallback}
          </AvatarFallback>
        </Avatar>

        <div className="ml-1 flex flex-col justify-center">
          <p className="text-sm font-medium capitalize">{user.username}</p>
          <p className="text-muted-foreground text-sm">Settings</p>
        </div>
      </NavLink>
    );
  }

  // Show anonymous user profile
  if (anonUser?.isAnonymous) {
    return (
      <div className="flex gap-2 rounded-md border border-transparent p-2">
        <Avatar className="size-11 rounded-md">
          <AvatarFallback className="bg-orange-500 text-sm text-white">
            <UserIcon className="size-5" />
          </AvatarFallback>
        </Avatar>

        <div className="ml-1 flex flex-col justify-center">
          <p className="text-sm font-medium">Anonymous User</p>
          <p className="text-muted-foreground text-sm">Guest Session</p>
        </div>
      </div>
    );
  }

  // Show login prompt
  return <LoginModel />;
}

function LoginModel() {
  return (
    <NavLink
      to="/auth/login"
      className="hover:bg-primary/20 hover:border-primary/30 flex gap-2 rounded-md border border-transparent p-2 transition-colors"
    >
      <Avatar className="size-11 rounded-md">
        <AvatarFallback className="bg-primary text-primary-foreground text-xs">
          Login
        </AvatarFallback>
      </Avatar>

      <div className="ml-1 flex flex-col justify-center">
        <p className="text-sm font-medium capitalize">Login</p>
        <p className="text-muted-foreground text-sm">To get more usages</p>
      </div>
    </NavLink>
  );
}
