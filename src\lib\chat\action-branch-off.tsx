import { api } from "@/convex/_generated/api";

import type { useNavigate } from "react-router";

import { getConvexReactClient } from "../convex/client";
import type { ChatMessage } from "../types";
import { toUUID } from "../utils";

const convexClient = getConvexReactClient();

// Helper function to get anonymous session ID
function getAnonymousSessionId(): string | null {
  if (typeof window === "undefined") return null;
  return localStorage.getItem("anonymous_session_id");
}

export async function branchOffThreadMessage(
  message: ChatMessage,
  navigate: ReturnType<typeof useNavigate>,
) {
  console.log("Branch off", message._creationTime);

  const anonymousId = getAnonymousSessionId();

  const newThreadId = await convexClient.mutation(api.threads.branchThread, {
    lastMessageCreatedAt: message._creationTime,
    threadId: message.threadId,
    anonymousId,
  });

  await navigate(`/chat/${toUUID(newThreadId)}`);
}
